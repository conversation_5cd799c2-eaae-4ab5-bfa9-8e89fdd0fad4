"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { App, APP_TYPE_TEXT } from "@/core.constants";
import {
  Activity,
  AlertTriangle,
  ExternalLink,
  Phone,
  Shield,
  Download,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";

interface AppDetailsModalProps {
  app: App | null;
  isOpen: boolean;
  onClose: () => void;
}

export const AppDetailsModal = ({
  app,
  isOpen,
  onClose,
}: AppDetailsModalProps) => {
  if (!app) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-full sm:min-w-[50vw] max-h-[95vh] overflow-y-auto bg-gradient-to-br from-neutral-900 via-neutral-900 to-neutral-800 border-white/10 shadow-2xl">
        <DialogHeader className="space-y-6 pb-8 border-b border-white/10">
          <div className="flex items-start space-x-6">
            {app.appLogo ? (
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
                <Image
                  src={app.appLogo}
                  alt={`${app.name} logo`}
                  width={80}
                  height={80}
                  className="relative w-20 h-20 object-contain rounded-2xl shadow-2xl border border-white/10 bg-white/5 backdrop-blur-sm"
                />
                <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-to-r from-primary to-primary/80 rounded-full flex items-center justify-center shadow-lg border-2 border-neutral-900">
                  <Shield className="w-4 h-4 text-white" />
                </div>
              </div>
            ) : (
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
                <div className="relative w-20 h-20 bg-gradient-to-br from-primary via-primary/90 to-primary/70 rounded-2xl flex items-center justify-center shadow-2xl border border-white/10">
                  <span className="text-3xl font-bold text-white">
                    {app.name.charAt(0)}
                  </span>
                </div>
                <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-gradient-to-r from-primary to-primary/80 rounded-full flex items-center justify-center shadow-lg border-2 border-neutral-900">
                  <Shield className="w-4 h-4 text-white" />
                </div>
              </div>
            )}
            <div className="flex-1 space-y-3">
              <DialogTitle className="text-4xl font-bold bg-gradient-to-r from-white via-white to-neutral-300 bg-clip-text text-transparent">
                {app.name}
              </DialogTitle>
              <Badge
                variant="secondary"
                className="text-sm px-4 py-2 bg-white/10 text-white border-white/20 backdrop-blur-sm hover:bg-white/20 transition-colors"
              >
                {APP_TYPE_TEXT[app.type]}
              </Badge>
              <p className="text-neutral-400 text-sm leading-relaxed max-w-2xl">
                Comprehensive security evaluation and configuration management
                for your {app.name} account.
              </p>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-12 pt-2">
          <div>
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <Shield className="w-3 h-3 text-white" />
              </div>
              <h2 className="text-lg font-bold text-white">
                Security Features
              </h2>
              <div className="flex-1 h-px bg-gradient-to-r from-white/20 to-transparent" />
            </div>
            <div className="grid grid-cols-3 gap-3">
              <div className="text-center p-3 rounded-lg border border-white/10 bg-white/5 hover:bg-white/10 transition-colors">
                <div className="flex justify-center mb-2">
                  <div className="rounded-lg p-2 bg-blue-500">
                    <Shield className="w-4 h-4 text-white" />
                  </div>
                </div>
                <div className="text-xs text-white font-medium">Yubikey</div>
                <div
                  className={`text-xs mt-1 mb-2 ${
                    app.yubikeys?.enabled ? "text-green-400" : "text-red-400"
                  }`}
                >
                  {app.yubikeys?.enabled ? "Supported" : "Not Available"}
                </div>
                {app.yubikeys?.enabled && app.yubikeys?.link && (
                  <Button
                    asChild
                    size="sm"
                    variant="outline"
                    className="h-6 text-xs px-2 bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30"
                  >
                    <Link
                      href={app.yubikeys.link}
                      target="_blank"
                      className="flex items-center"
                    >
                      <ExternalLink className="w-3 h-3 mr-1" />
                      Setup
                    </Link>
                  </Button>
                )}
              </div>
              <div className="text-center p-3 rounded-lg border border-white/10 bg-white/5 hover:bg-white/10 transition-colors">
                <div className="flex justify-center mb-2">
                  <div
                    className={`rounded-lg p-2 ${
                      app.phoneNumber?.enabled ? "bg-red-500" : "bg-green-500"
                    }`}
                  >
                    <Phone className="w-4 h-4 text-white" />
                  </div>
                </div>
                <div className="text-xs text-white font-medium">Phone</div>
                <div
                  className={`text-xs mt-1 mb-2 ${
                    app.phoneNumber?.enabled ? "text-red-400" : "text-green-400"
                  }`}
                >
                  {app.phoneNumber?.enabled ? "Required" : "Optional"}
                </div>
                {app.phoneNumber?.link && (
                  <Button
                    asChild
                    size="sm"
                    variant="outline"
                    className="h-6 text-xs px-2 bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30"
                  >
                    <Link
                      href={app.phoneNumber.link}
                      target="_blank"
                      className="flex items-center"
                    >
                      <ExternalLink className="w-3 h-3 mr-1" />
                      {app.phoneNumber?.enabled ? "Remove" : "Manage"}
                    </Link>
                  </Button>
                )}
              </div>
              <div className="text-center p-3 rounded-lg border border-white/10 bg-white/5 hover:bg-white/10 transition-colors">
                <div className="flex justify-center mb-2">
                  <div className="rounded-lg p-2 bg-purple-500">
                    <Download className="w-4 h-4 text-white" />
                  </div>
                </div>
                <div className="text-xs text-white font-medium">Backup</div>
                <div
                  className={`text-xs mt-1 mb-2 ${
                    app.backup?.enabled ? "text-green-400" : "text-red-400"
                  }`}
                >
                  {app.backup?.enabled ? "Available" : "Not Available"}
                </div>
                {app.backup?.enabled && app.backup?.link && (
                  <Button
                    asChild
                    size="sm"
                    variant="outline"
                    className="h-6 text-xs px-2 bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30"
                  >
                    <Link
                      href={app.backup.link}
                      target="_blank"
                      className="flex items-center"
                    >
                      <ExternalLink className="w-3 h-3 mr-1" />
                      Access
                    </Link>
                  </Button>
                )}
              </div>
            </div>
          </div>

          <div>
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-orange-500 rounded-lg flex items-center justify-center shadow-lg">
                <AlertTriangle className="w-4 h-4 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-white">
                Emergency Actions
              </h2>
              <div className="flex-1 h-px bg-gradient-to-r from-red-500/30 to-transparent" />
            </div>

            {app.emergency.links.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {app.emergency.links.map((emergencyLink, index) => (
                  <div
                    key={`emergency-${index}-${emergencyLink.description}`}
                    className="group relative overflow-hidden rounded-2xl border border-red-500/20 backdrop-blur-sm p-6 transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl hover:shadow-red-500/20 hover:border-red-400/40"
                  >
                    <div className="absolute inset-0 bg-gradient-to-br  opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    <div className="absolute top-4 right-4 w-2 h-2  rounded-full animate-pulse" />

                    <div className="relative z-10">
                      <Button
                        asChild
                        className="w-full bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 h-12"
                      >
                        <Link
                          href={emergencyLink.link}
                          target="_blank"
                          className="flex items-center justify-center"
                        >
                          <AlertTriangle className="w-5 h-5 mr-3" />
                          {emergencyLink.description}
                          <ExternalLink className="w-4 h-4 ml-3 opacity-70" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {app.emergency.instructions.length > 0 && (
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                  <Activity className="w-4 h-4 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-white">
                  Security Instructions
                </h2>
                <div className="flex-1 h-px bg-gradient-to-r from-white/20 to-transparent" />
              </div>
              <div className="space-y-6">
                {app.emergency.instructions.map((instruction, index) => (
                  <div
                    key={`instruction-${index}-${instruction.description.slice(
                      0,
                      20
                    )}`}
                    className="rounded-2xl border border-white/10 bg-gradient-to-br from-neutral-900/50 to-neutral-800/50 backdrop-blur-sm p-6 hover:border-white/20 transition-all duration-300"
                  >
                    <p className="text-neutral-300 leading-relaxed mb-4">
                      {instruction.description}
                    </p>
                    {instruction.imageLink && (
                      <div className="mt-4">
                        <Image
                          src={instruction.imageLink}
                          alt="Security instruction visual guide"
                          width={400}
                          height={200}
                          className="rounded-xl border border-white/10 shadow-lg w-full object-cover"
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
